#!/usr/bin/env python3
"""
安全清理脚本 - 清理可能泄露数据的通知配置
"""

import sqlite3
import os
import sys
from loguru import logger

class SecurityCleanup:
    def __init__(self, db_path='xianyu_data.db'):
        self.db_path = db_path
        if not os.path.exists(db_path):
            logger.error(f"数据库文件不存在: {db_path}")
            sys.exit(1)
        
        self.conn = sqlite3.connect(db_path)
        logger.info(f"连接到数据库: {db_path}")
    
    def check_notification_channels(self):
        """检查通知渠道配置"""
        cursor = self.conn.cursor()
        
        # 查询所有通知渠道
        cursor.execute('''
        SELECT id, name, type, config, enabled, created_at 
        FROM notification_channels 
        ORDER BY id
        ''')
        
        channels = cursor.fetchall()
        
        if not channels:
            logger.info("✅ 未发现任何通知渠道配置")
            return []
        
        logger.warning(f"🔍 发现 {len(channels)} 个通知渠道:")
        
        risky_channels = []
        for channel in channels:
            channel_id, name, channel_type, config, enabled, created_at = channel
            status = "启用" if enabled else "禁用"
            
            logger.warning(f"  ID: {channel_id}")
            logger.warning(f"  名称: {name}")
            logger.warning(f"  类型: {channel_type}")
            logger.warning(f"  配置: {config}")
            logger.warning(f"  状态: {status}")
            logger.warning(f"  创建时间: {created_at}")
            logger.warning("  " + "-" * 40)
            
            # 检查是否是第三方QQ通知服务
            if channel_type == 'qq' and 'zhinianblog.cn' in str(config):
                risky_channels.append(channel_id)
                logger.error(f"  ⚠️  这是一个有风险的第三方QQ通知渠道!")
        
        return risky_channels
    
    def check_message_notifications(self):
        """检查消息通知配置"""
        cursor = self.conn.cursor()
        
        # 查询所有消息通知配置
        cursor.execute('''
        SELECT mn.id, mn.cookie_id, mn.channel_id, mn.enabled, 
               nc.name, nc.type, nc.config
        FROM message_notifications mn
        JOIN notification_channels nc ON mn.channel_id = nc.id
        ORDER BY mn.cookie_id, mn.id
        ''')
        
        notifications = cursor.fetchall()
        
        if not notifications:
            logger.info("✅ 未发现任何消息通知配置")
            return {}
        
        logger.warning(f"🔍 发现 {len(notifications)} 个消息通知配置:")
        
        risky_notifications = {}
        for notification in notifications:
            notif_id, cookie_id, channel_id, enabled, channel_name, channel_type, channel_config = notification
            status = "启用" if enabled else "禁用"
            
            logger.warning(f"  通知ID: {notif_id}")
            logger.warning(f"  账号ID: {cookie_id}")
            logger.warning(f"  渠道: {channel_name} ({channel_type})")
            logger.warning(f"  配置: {channel_config}")
            logger.warning(f"  状态: {status}")
            logger.warning("  " + "-" * 40)
            
            # 检查是否是第三方服务
            if channel_type == 'qq' and 'zhinianblog.cn' in str(channel_config):
                if cookie_id not in risky_notifications:
                    risky_notifications[cookie_id] = []
                risky_notifications[cookie_id].append(notif_id)
                logger.error(f"  ⚠️  这个通知配置会向第三方服务发送敏感数据!")
        
        return risky_notifications
    
    def disable_risky_channels(self, channel_ids):
        """禁用有风险的通知渠道"""
        if not channel_ids:
            return
        
        cursor = self.conn.cursor()
        
        for channel_id in channel_ids:
            cursor.execute('''
            UPDATE notification_channels 
            SET enabled = 0, updated_at = CURRENT_TIMESTAMP 
            WHERE id = ?
            ''', (channel_id,))
            
            logger.info(f"✅ 已禁用风险通知渠道 ID: {channel_id}")
        
        self.conn.commit()
    
    def delete_risky_notifications(self, risky_notifications):
        """删除有风险的消息通知配置"""
        if not risky_notifications:
            return
        
        cursor = self.conn.cursor()
        
        for cookie_id, notification_ids in risky_notifications.items():
            for notif_id in notification_ids:
                cursor.execute('''
                DELETE FROM message_notifications WHERE id = ?
                ''', (notif_id,))
                
                logger.info(f"✅ 已删除账号 {cookie_id} 的风险通知配置 ID: {notif_id}")
        
        self.conn.commit()
    
    def run_security_check(self):
        """运行安全检查"""
        logger.info("🔒 开始安全检查...")
        
        # 检查通知渠道
        risky_channels = self.check_notification_channels()
        
        # 检查消息通知配置
        risky_notifications = self.check_message_notifications()
        
        # 汇总风险
        total_risks = len(risky_channels) + sum(len(notifs) for notifs in risky_notifications.values())
        
        if total_risks == 0:
            logger.info("✅ 安全检查完成，未发现风险配置")
            return
        
        logger.error(f"⚠️  发现 {total_risks} 个风险配置项")
        logger.error("这些配置可能会向第三方服务泄露您的账号和客户数据!")
        
        return risky_channels, risky_notifications
    
    def run_security_cleanup(self):
        """运行安全清理"""
        result = self.run_security_check()
        
        if not result:
            return
        
        risky_channels, risky_notifications = result
        
        print("\n" + "="*60)
        print("🚨 发现安全风险，建议立即清理!")
        print("="*60)
        
        if risky_channels:
            print(f"风险通知渠道: {len(risky_channels)} 个")
        
        if risky_notifications:
            total_notifs = sum(len(notifs) for notifs in risky_notifications.values())
            print(f"风险通知配置: {total_notifs} 个")
        
        print("\n是否要自动清理这些风险配置? (y/N): ", end="")
        choice = input().strip().lower()
        
        if choice in ['y', 'yes']:
            # 禁用风险渠道
            self.disable_risky_channels(risky_channels)
            
            # 删除风险通知配置
            self.delete_risky_notifications(risky_notifications)
            
            logger.info("🎉 安全清理完成!")
            logger.info("建议重启应用以使配置生效")
        else:
            logger.warning("⚠️  已跳过自动清理，请手动处理风险配置")
    
    def close(self):
        """关闭数据库连接"""
        if self.conn:
            self.conn.close()

def main():
    """主函数"""
    print("🔒 咸鱼自动回复项目 - 安全清理工具")
    print("="*50)
    
    # 检查数据库文件
    db_files = ['xianyu_data.db', 'data/xianyu_data.db']
    db_path = None
    
    for path in db_files:
        if os.path.exists(path):
            db_path = path
            break
    
    if not db_path:
        logger.error("未找到数据库文件，请确保在项目根目录运行此脚本")
        sys.exit(1)
    
    # 运行安全清理
    cleanup = SecurityCleanup(db_path)
    try:
        cleanup.run_security_cleanup()
    finally:
        cleanup.close()

if __name__ == "__main__":
    main()
