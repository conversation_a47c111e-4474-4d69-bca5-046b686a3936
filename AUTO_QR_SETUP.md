# 🤖 自动扫码登录设置指南

## 🎯 问题解决

如果您点击"启动自动扫码"按钮没有反应，这是因为缺少Playwright依赖。

## 🚀 快速解决方案

### 方法一：自动安装（推荐）
```bash
python install_auto_qr.py
```

### 方法二：手动安装
```bash
# 1. 安装Playwright包
pip install playwright

# 2. 安装浏览器
playwright install chromium
```

## 📋 详细步骤

### 1. 检查Python版本
确保您使用的是Python 3.8或更高版本：
```bash
python --version
```

### 2. 运行安装脚本
在项目根目录执行：
```bash
python install_auto_qr.py
```

安装脚本会：
- ✅ 检查Python版本
- ✅ 安装Playwright包
- ✅ 下载Chromium浏览器
- ✅ 测试安装是否成功

### 3. 重启服务
安装完成后，重启服务：
```bash
python Start.py
```

### 4. 测试功能
1. 访问 `http://localhost:8080`
2. 选择"扫码登录"选项卡
3. 点击"启动自动扫码"按钮
4. 应该会自动打开浏览器并显示二维码

## 🔧 故障排除

### 问题1：安装失败
**解决方案：**
- 使用管理员权限运行命令
- 检查网络连接
- 尝试使用国内镜像：
  ```bash
  pip install playwright -i https://pypi.tuna.tsinghua.edu.cn/simple
  ```

### 问题2：浏览器下载失败
**解决方案：**
- 检查防火墙设置
- 手动设置代理：
  ```bash
  set HTTPS_PROXY=http://your-proxy:port
  playwright install chromium
  ```

### 问题3：权限错误
**解决方案：**
- Windows：以管理员身份运行命令提示符
- macOS/Linux：使用 `sudo` 命令

### 问题4：磁盘空间不足
**解决方案：**
- 清理磁盘空间（需要约200MB）
- 或者使用手动Cookie获取方式

## 📱 备用方案

如果自动扫码仍然无法使用，您可以使用手动方式：

1. 在扫码登录页面右侧，点击"手动输入Cookie"
2. 点击咸鱼官网链接
3. 手动扫码登录
4. 按F12获取Cookie
5. 粘贴到输入框并保存

## 🎉 成功标志

安装成功后，您应该看到：
- ✅ 点击"启动自动扫码"按钮有反应
- ✅ 自动打开Chrome浏览器
- ✅ 浏览器自动访问咸鱼官网
- ✅ 管理界面显示二维码
- ✅ 状态显示"等待扫码..."

## 💡 提示

- 首次运行可能需要几秒钟启动浏览器
- 确保系统有足够的内存运行浏览器
- 如果遇到问题，查看浏览器控制台的错误信息
- 可以在浏览器中按F12查看详细的错误日志

## 📞 技术支持

如果仍然遇到问题：
1. 检查控制台错误信息
2. 确认Python和pip版本
3. 尝试重新安装依赖
4. 使用手动Cookie获取作为备用方案
