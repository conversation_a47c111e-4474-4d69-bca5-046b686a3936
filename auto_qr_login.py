#!/usr/bin/env python3
"""
自动化扫码登录模块
使用playwright实现自动化浏览器操作，扫码后自动获取Cookie
"""

import asyncio
import json
import time
from typing import Dict, Optional, Tuple
from playwright.async_api import async_playwright, <PERSON><PERSON>er, BrowserContext, Page
from loguru import logger
import uuid


class AutoQrLogin:
    def __init__(self):
        self.browser: Optional[Browser] = None
        self.context: Optional[BrowserContext] = None
        self.page: Optional[Page] = None
        self.session_id: str = ""
        self.status: str = "waiting"  # waiting, qr_ready, scanned, success, failed, expired
        self.cookie_value: str = ""
        self.user_info: Dict = {}
        self.qr_image_data: str = ""
        
    async def start_browser(self, headless: bool = False):
        """启动浏览器"""
        try:
            self.playwright = await async_playwright().start()
            
            # 启动浏览器（建议使用非无头模式以便用户看到二维码）
            self.browser = await self.playwright.chromium.launch(
                headless=headless,
                args=['--no-sandbox', '--disable-setuid-sandbox']
            )
            
            # 创建浏览器上下文
            self.context = await self.browser.new_context(
                viewport={'width': 1280, 'height': 720},
                user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            )
            
            # 创建页面
            self.page = await self.context.new_page()
            
            logger.info("浏览器启动成功")
            return True
            
        except Exception as e:
            logger.error(f"启动浏览器失败: {str(e)}")
            return False
    
    async def navigate_to_login(self):
        """导航到咸鱼登录页面"""
        try:
            # 访问咸鱼官网
            await self.page.goto('https://www.goofish.com', wait_until='networkidle')
            
            # 等待页面加载
            await asyncio.sleep(2)
            
            # 查找登录按钮并点击
            login_selectors = [
                'text=登录',
                '[data-spm-click*="login"]',
                '.login-btn',
                'a[href*="login"]'
            ]
            
            login_clicked = False
            for selector in login_selectors:
                try:
                    login_element = await self.page.wait_for_selector(selector, timeout=3000)
                    if login_element:
                        await login_element.click()
                        login_clicked = True
                        logger.info(f"点击登录按钮成功: {selector}")
                        break
                except:
                    continue
            
            if not login_clicked:
                # 如果没有找到登录按钮，尝试直接访问登录页面
                await self.page.goto('https://login.goofish.com', wait_until='networkidle')
            
            await asyncio.sleep(2)
            
            # 查找扫码登录选项
            qr_selectors = [
                'text=扫码登录',
                '.qr-login',
                '[data-spm-click*="qr"]',
                'text=二维码登录'
            ]
            
            for selector in qr_selectors:
                try:
                    qr_element = await self.page.wait_for_selector(selector, timeout=3000)
                    if qr_element:
                        await qr_element.click()
                        logger.info(f"切换到扫码登录成功: {selector}")
                        break
                except:
                    continue
            
            self.status = "qr_ready"
            return True
            
        except Exception as e:
            logger.error(f"导航到登录页面失败: {str(e)}")
            self.status = "failed"
            return False
    
    async def wait_for_qr_code(self) -> bool:
        """等待二维码出现并获取"""
        try:
            # 等待二维码元素出现
            qr_selectors = [
                'img[src*="qr"]',
                '.qr-code img',
                '[class*="qr"] img',
                'canvas[class*="qr"]'
            ]
            
            qr_element = None
            for selector in qr_selectors:
                try:
                    qr_element = await self.page.wait_for_selector(selector, timeout=10000)
                    if qr_element:
                        logger.info(f"找到二维码元素: {selector}")
                        break
                except:
                    continue
            
            if not qr_element:
                logger.error("未找到二维码元素")
                return False
            
            # 获取二维码图片
            try:
                # 如果是img元素，获取src
                if await qr_element.get_attribute('src'):
                    self.qr_image_data = await qr_element.get_attribute('src')
                else:
                    # 如果是canvas，截图
                    screenshot = await qr_element.screenshot()
                    import base64
                    self.qr_image_data = f"data:image/png;base64,{base64.b64encode(screenshot).decode()}"
                
                logger.info("二维码获取成功")
                return True
                
            except Exception as e:
                logger.error(f"获取二维码图片失败: {str(e)}")
                return False
                
        except Exception as e:
            logger.error(f"等待二维码失败: {str(e)}")
            return False
    
    async def monitor_login_status(self, timeout: int = 300) -> Tuple[bool, str, Dict]:
        """监控登录状态"""
        start_time = time.time()
        
        try:
            while time.time() - start_time < timeout:
                # 检查是否已经登录成功（通过URL变化或页面元素）
                current_url = self.page.url
                
                # 检查是否跳转到了主页或其他登录后的页面
                if 'goofish.com' in current_url and 'login' not in current_url:
                    logger.info("检测到登录成功，开始获取Cookie")
                    
                    # 获取所有Cookie
                    cookies = await self.context.cookies()
                    
                    # 格式化Cookie为字符串
                    cookie_parts = []
                    for cookie in cookies:
                        cookie_parts.append(f"{cookie['name']}={cookie['value']}")
                    
                    self.cookie_value = "; ".join(cookie_parts)
                    
                    # 尝试获取用户信息
                    try:
                        # 查找用户昵称或头像等元素
                        user_selectors = [
                            '[class*="nick"]',
                            '[class*="username"]',
                            '[class*="user-name"]'
                        ]
                        
                        for selector in user_selectors:
                            try:
                                user_element = await self.page.wait_for_selector(selector, timeout=2000)
                                if user_element:
                                    nick = await user_element.inner_text()
                                    self.user_info = {"nick": nick.strip()}
                                    break
                            except:
                                continue
                                
                    except Exception as e:
                        logger.warning(f"获取用户信息失败: {str(e)}")
                        self.user_info = {"nick": f"用户_{int(time.time()) % 10000}"}
                    
                    self.status = "success"
                    logger.info("登录成功，Cookie获取完成")
                    return True, self.cookie_value, self.user_info
                
                # 检查是否有错误信息
                error_selectors = [
                    'text=登录失败',
                    'text=二维码已过期',
                    'text=登录超时',
                    '[class*="error"]'
                ]
                
                for selector in error_selectors:
                    try:
                        error_element = await self.page.wait_for_selector(selector, timeout=1000)
                        if error_element:
                            error_text = await error_element.inner_text()
                            logger.warning(f"检测到错误信息: {error_text}")
                            if "过期" in error_text:
                                self.status = "expired"
                                return False, "", {}
                    except:
                        continue
                
                # 等待一段时间再检查
                await asyncio.sleep(2)
            
            # 超时
            self.status = "expired"
            logger.warning("登录监控超时")
            return False, "", {}
            
        except Exception as e:
            logger.error(f"监控登录状态失败: {str(e)}")
            self.status = "failed"
            return False, "", {}
    
    async def close(self):
        """关闭浏览器"""
        try:
            if self.page:
                await self.page.close()
            if self.context:
                await self.context.close()
            if self.browser:
                await self.browser.close()
            if hasattr(self, 'playwright'):
                await self.playwright.stop()
            logger.info("浏览器已关闭")
        except Exception as e:
            logger.error(f"关闭浏览器失败: {str(e)}")
    
    async def start_qr_login(self, session_id: str = None, headless: bool = False) -> Dict:
        """启动完整的扫码登录流程"""
        self.session_id = session_id or str(uuid.uuid4())
        
        try:
            # 启动浏览器
            if not await self.start_browser(headless=headless):
                return {
                    "success": False,
                    "session_id": self.session_id,
                    "status": "failed",
                    "message": "启动浏览器失败"
                }
            
            # 导航到登录页面
            if not await self.navigate_to_login():
                await self.close()
                return {
                    "success": False,
                    "session_id": self.session_id,
                    "status": "failed",
                    "message": "导航到登录页面失败"
                }
            
            # 等待二维码
            if not await self.wait_for_qr_code():
                await self.close()
                return {
                    "success": False,
                    "session_id": self.session_id,
                    "status": "failed",
                    "message": "获取二维码失败"
                }
            
            return {
                "success": True,
                "session_id": self.session_id,
                "status": "qr_ready",
                "qr_image_data": self.qr_image_data,
                "message": "二维码已准备就绪，请使用手机扫码"
            }
            
        except Exception as e:
            logger.error(f"启动扫码登录失败: {str(e)}")
            await self.close()
            return {
                "success": False,
                "session_id": self.session_id,
                "status": "failed",
                "message": f"启动失败: {str(e)}"
            }


# 全局会话管理
active_sessions: Dict[str, AutoQrLogin] = {}


async def cleanup_session(session_id: str):
    """清理会话"""
    if session_id in active_sessions:
        await active_sessions[session_id].close()
        del active_sessions[session_id]
        logger.info(f"会话已清理: {session_id}")
