#!/usr/bin/env python3
"""
自动扫码登录依赖安装脚本
"""

import subprocess
import sys
import os
from pathlib import Path

def run_command(command, description):
    """运行命令并显示结果"""
    print(f"\n🔧 {description}")
    print(f"执行命令: {command}")
    
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"✅ {description} 成功")
            if result.stdout:
                print(f"输出: {result.stdout.strip()}")
        else:
            print(f"❌ {description} 失败")
            if result.stderr:
                print(f"错误: {result.stderr.strip()}")
            return False
            
    except Exception as e:
        print(f"❌ {description} 异常: {str(e)}")
        return False
    
    return True

def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    print(f"🐍 Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ 需要Python 3.8或更高版本")
        return False
    
    print("✅ Python版本符合要求")
    return True

def install_playwright():
    """安装Playwright"""
    print("\n" + "="*50)
    print("📦 安装自动扫码登录依赖")
    print("="*50)
    
    # 安装playwright包
    if not run_command("pip install playwright", "安装Playwright包"):
        return False
    
    # 安装浏览器
    if not run_command("playwright install chromium", "安装Chromium浏览器"):
        return False
    
    return True

def test_installation():
    """测试安装是否成功"""
    print("\n🧪 测试安装...")
    
    try:
        from playwright.sync_api import sync_playwright
        print("✅ Playwright导入成功")
        
        # 测试浏览器启动
        with sync_playwright() as p:
            browser = p.chromium.launch(headless=True)
            page = browser.new_page()
            page.goto("https://www.baidu.com")
            title = page.title()
            browser.close()
            print(f"✅ 浏览器测试成功，访问页面标题: {title}")
        
        return True
        
    except ImportError as e:
        print(f"❌ Playwright导入失败: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ 浏览器测试失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🤖 自动扫码登录依赖安装程序")
    print("="*50)
    
    # 检查Python版本
    if not check_python_version():
        print("\n❌ 安装失败：Python版本不符合要求")
        return False
    
    # 安装依赖
    if not install_playwright():
        print("\n❌ 安装失败：无法安装Playwright")
        return False
    
    # 测试安装
    if not test_installation():
        print("\n❌ 安装失败：测试不通过")
        return False
    
    print("\n" + "="*50)
    print("🎉 自动扫码登录依赖安装成功！")
    print("="*50)
    print("\n📋 接下来的步骤：")
    print("1. 重启服务：python Start.py")
    print("2. 访问管理界面：http://localhost:8080")
    print("3. 选择'扫码登录'选项卡")
    print("4. 点击'启动自动扫码'按钮")
    print("\n🎯 现在您可以享受全自动扫码登录了！")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            print("\n💡 如果遇到问题，请尝试：")
            print("1. 使用管理员权限运行此脚本")
            print("2. 检查网络连接")
            print("3. 手动执行命令：")
            print("   pip install playwright")
            print("   playwright install chromium")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n\n⏹️ 安装被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 安装过程中发生异常: {str(e)}")
        sys.exit(1)
