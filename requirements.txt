# Web框架和API相关
fastapi>=0.111
uvicorn[standard]>=0.29
pydantic>=2.7

# 日志记录
loguru>=0.7

# 网络通信
websockets>=10.0,<13.0  # 兼容性版本范围
aiohttp>=3.9

# 配置文件处理
PyYAML>=6.0

# JavaScript执行引擎
PyExecJS>=1.5.1

# 协议缓冲区解析
blackboxprotobuf>=1.0.1

# 系统监控
psutil>=5.9.0

# HTTP客户端（用于测试）
requests>=2.31.0

# 文件上传支持
python-multipart>=0.0.6

# AI回复相关
openai>=1.65.5
python-dotenv>=1.0.1

# 图像处理（图形验证码）
Pillow>=10.0.0

# 浏览器自动化（商品搜索功能）
playwright>=1.40.0

# 加密和安全
PyJWT>=2.8.0
passlib>=1.7.4
bcrypt>=4.0.1

# 时间处理
python-dateutil>=2.8.2

# 正则表达式增强
regex>=2023.10.3

# Excel文件处理（导入导出功能）
pandas>=2.0.0
openpyxl>=3.1.0